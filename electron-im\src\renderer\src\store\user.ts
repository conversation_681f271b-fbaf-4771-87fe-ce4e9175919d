// 用户状态管理 - Pinia Store
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { apiClient, type User } from '../api'

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<User | null>(null)
  const isAuthenticated = ref(false)
  const token = ref<string | null>(null)

  // 计算属性
  const userDisplayName = computed(() => {
    return currentUser.value?.displayName || currentUser.value?.username || ''
  })

  const userAvatar = computed(() => {
    return currentUser.value?.avatar || '/avatars/default.png'
  })

  // 初始化用户状态
  const initializeAuth = () => {
    console.log('初始化用户认证状态...')
    const user = apiClient.getCurrentUser()
    const userToken = apiClient.getToken()

    console.log('从localStorage获取的用户:', user)
    console.log('从localStorage获取的token:', userToken)

    if (user && userToken) {
      currentUser.value = user
      token.value = userToken
      isAuthenticated.value = true
      console.log('用户认证状态初始化成功:', { user, isAuthenticated: true })
    } else {
      console.log('用户未登录或token无效')
    }
  }

  // 登录
  const login = async (username: string, password: string) => {
    try {
      console.log('开始登录请求:', { username })
      const response = await apiClient.login({ username, password })
      console.log('登录响应:', response)

      if (response.success) {
        currentUser.value = response.user
        token.value = response.token
        isAuthenticated.value = true
        console.log('用户状态更新完成:', {
          currentUser: currentUser.value,
          token: token.value,
          isAuthenticated: isAuthenticated.value
        })
        return { success: true, message: '登录成功' }
      } else {
        console.log('登录失败:', response)
        return { success: false, message: '登录失败' }
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  // 登出
  const logout = () => {
    apiClient.logout()
    currentUser.value = null
    token.value = null
    isAuthenticated.value = false
  }

  return {
    // 状态
    currentUser,
    isAuthenticated,
    token,

    // 计算属性
    userDisplayName,
    userAvatar,

    // 方法
    initializeAuth,
    login,
    logout
  }
})
