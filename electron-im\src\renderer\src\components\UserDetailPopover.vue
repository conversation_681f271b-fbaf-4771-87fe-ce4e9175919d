<template>
  <a-popover v-model:open="popoverVisible" placement="bottomLeft" trigger="click">
    <template #content>
      <div class="w-80 max-w-sm">
        <div v-if="isLoading" class="p-4 text-center">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
          <p class="mt-2 text-xs text-gray-500">加载中...</p>
        </div>

        <div v-else-if="error" class="p-4 text-center">
          <p class="text-red-600 text-xs">{{ error }}</p>
          <button
            class="mt-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-xs"
            @click="loadUserDetail"
          >
            重试
          </button>
        </div>

        <div v-else-if="userDetail" class="p-4">
          <div class="flex items-center mb-4">
            <UserAvatar
              :name="userDetail.displayName"
              size="medium"
              :is-online="userDetail.isOnline"
              :show-online-status="true"
            />
            <div class="ml-3">
              <h4 class="text-sm font-medium text-gray-900">{{ userDetail.displayName }}</h4>
              <p class="text-xs text-gray-500">@{{ userDetail.username }}</p>
              <p class="text-xs text-gray-400 mt-1">
                {{ userDetail.isOnline ? '在线' : '离线' }}
                <span v-if="!userDetail.isOnline">
                  · {{ formatTime(userDetail.lastOnlineTime) }}
                </span>
              </p>
            </div>
          </div>

          <div class="space-y-3">
            <div>
              <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide"
                >邮箱</label
              >
              <p class="mt-1 text-xs text-gray-900">{{ userDetail.email }}</p>
            </div>

            <div>
              <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide"
                >部门</label
              >
              <p class="mt-1 text-xs text-gray-900">{{ userDetail.department }}</p>
            </div>

            <div>
              <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide"
                >职位</label
              >
              <p class="mt-1 text-xs text-gray-900">{{ userDetail.position }}</p>
            </div>
          </div>
        </div>
      </div>
    </template>

    <slot></slot>
  </a-popover>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { apiClient, type User } from '../api'
import UserAvatar from './UserAvatar.vue'

interface Props {
  userId: string | null
  open?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:open': [value: boolean]
}>()

const isLoading = ref(false)
const error = ref('')
const userDetail = ref<User | null>(null)

// 控制 Popover 显示状态
const popoverVisible = computed({
  get: () => props.open || false,
  set: (value) => emit('update:open', value)
})

// 监听用户ID变化和弹窗显示状态
watch(
  () => [props.open, props.userId],
  ([visible, userId]) => {
    if (visible && userId) {
      loadUserDetail()
    } else {
      // 重置状态
      userDetail.value = null
      error.value = ''
    }
  },
  { immediate: true }
)

// 加载用户详情
const loadUserDetail = async () => {
  if (!props.userId) return

  try {
    isLoading.value = true
    error.value = ''

    const response = await apiClient.getUserDetail(props.userId)

    if (response.success) {
      userDetail.value = response.user
    } else {
      error.value = '获取用户信息失败'
    }
  } catch (err) {
    console.error('获取用户详情失败:', err)
    error.value = err instanceof Error ? err.message : '获取用户信息失败'
  } finally {
    isLoading.value = false
  }
}

// 时间格式化
const formatTime = (timeStr: string) => {
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) {
    // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) {
    // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}
</script>
