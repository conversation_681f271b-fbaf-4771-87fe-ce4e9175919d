<!-- 聊天主页 -->
<template>
  <div class="flex h-screen overflow-hidden bg-gray-50 font-sans">
    <!-- 左侧聊天列表 -->
    <ChatSidebar
      :contacts="chats"
      :current-contact-id="messageStore.currentChatUserId"
      :is-loading="isLoadingUsers"
      @select-contact="selectChat"
      @refresh="loadUsers"
    />

    <!-- 右侧聊天区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 聊天头部 -->
      <ChatHeader
        :current-contact="currentChat"
        @more-options="showMoreOptions"
        @logout="handleLogout"
      />

      <!-- 消息列表 -->
      <MessageList
        ref="messageListRef"
        :messages="currentMessages"
        :current-contact="currentChat"
      />

      <!-- 消息输入 -->
      <MessageInput
        :current-contact="currentChat"
        @send-message="sendMessage"
        @insert-emoji="insertEmoji"
        @attach-file="attachFile"
        @insert-image="insertImage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useUserStore } from '../store/user'
import { useMessageStore } from '../store/message'
import { apiClient } from '../api'
import type { User } from '../api'
import { WebSocketState } from '../services/websocketService'
import ChatSidebar from '../components/ChatSidebar.vue'
import ChatHeader from '../components/ChatHeader.vue'
import MessageList from '../components/MessageList.vue'
import MessageInput from '../components/MessageInput.vue'

// 状态管理
const userStore = useUserStore()
const messageStore = useMessageStore()

// 响应式数据
const isLoadingUsers = ref(false)
const messageListRef = ref<InstanceType<typeof MessageList>>()
const users = ref<User[]>([])

// 从API加载聊天联系人列表
const loadChatContacts = async () => {
  try {
    isLoadingUsers.value = true
    const response = await apiClient.getChatContacts()

    if (response.success) {
      console.log('加载聊天联系人成功:', response.contacts.length, '个联系人')

      // 将API返回的联系人数据转换为本地聊天会话格式
      const sessions = response.contacts.map((contact) => ({
        userId: contact.user.id,
        userName: contact.user.displayName,
        userAvatar: contact.user.avatar,
        lastMessage: contact.lastMessage
          ? {
              id: contact.lastMessage.id,
              senderId: contact.lastMessage.senderId,
              receiverId: contact.lastMessage.receiverId,
              content: contact.lastMessage.content,
              timestamp: new Date(contact.lastMessage.timestamp).getTime(),
              type: 1
            }
          : undefined,
        unreadCount: contact.unreadCount,
        lastActiveTime: new Date(contact.lastActiveTime).getTime()
      }))

      // 批量更新聊天会话
      messageStore.setChatSessions(sessions)

      // 同时将用户信息添加到用户列表中
      response.contacts.forEach((contact) => {
        const existingUser = users.value.find((u) => u.id === contact.user.id)
        if (!existingUser) {
          users.value.push(contact.user)
        }
      })

      console.log('聊天会话已更新:', messageStore.sortedChatSessions.length, '个会话')
    }
  } catch (error) {
    console.error('加载聊天联系人失败:', error)
  } finally {
    isLoadingUsers.value = false
  }
}

// 计算属性
const chats = computed(() => {
  // 只显示有聊天会话的用户
  return messageStore.sortedChatSessions
    .map((session) => {
      // 从用户列表中找到对应的用户信息
      const user = users.value.find((u) => u.id === session.userId)

      if (user) {
        return {
          id: user.id,
          name: user.displayName,
          avatar: getNameAvatar(user.displayName),
          status: user.isOnline ? '在线' : '离线',
          lastMessage: session.lastMessage?.content || '暂无消息',
          lastMessageTime: new Date(session.lastActiveTime),
          unreadCount: session.unreadCount,
          user: user
        }
      } else {
        // 如果在用户列表中找不到对应用户，使用会话中的信息
        return {
          id: session.userId,
          name: session.userName,
          avatar: getNameAvatar(session.userName),
          status: '离线', // 默认状态
          lastMessage: session.lastMessage?.content || '暂无消息',
          lastMessageTime: new Date(session.lastActiveTime),
          unreadCount: session.unreadCount,
          user: {
            id: session.userId,
            username: session.userId,
            email: '',
            displayName: session.userName,
            avatar: session.userAvatar,
            department: '',
            position: '',
            isOnline: false,
            lastOnlineTime: ''
          }
        }
      }
    })
    .filter(Boolean) // 过滤掉可能的空值
})

const currentChat = computed(() => {
  if (!messageStore.currentChatUserId) return null
  return chats.value.find((chat) => chat.id === messageStore.currentChatUserId)
})

const currentMessages = computed(() => {
  return messageStore.currentMessages.map((msg) => ({
    id: msg.id,
    senderId: msg.senderId,
    senderName:
      msg.senderId === userStore.currentUser?.id
        ? userStore.userDisplayName || '我'
        : users.value.find((u) => u.id === msg.senderId)?.displayName || '未知用户',
    content: msg.content,
    timestamp: new Date(msg.timestamp),
    isSending: msg.isSending,
    sendError: msg.sendError
  }))
})

const wsConnectionStatus = computed(() => {
  switch (messageStore.wsState) {
    case WebSocketState.CONNECTED:
      return '已连接'
    case WebSocketState.CONNECTING:
      return '连接中...'
    case WebSocketState.RECONNECTING:
      return '重连中...'
    case WebSocketState.DISCONNECTED:
      return '已断开'
    case WebSocketState.ERROR:
      return '连接错误'
    default:
      return '未知状态'
  }
})

// 组件挂载时的初始化
onMounted(async () => {
  // 检查用户是否已登录
  if (!userStore.isAuthenticated) {
    console.log('用户未登录，应该跳转到登录页')
    return
  }

  // 加载聊天联系人列表
  await loadChatContacts()

  // 初始化WebSocket连接
  try {
    await messageStore.initWebSocket(userStore.token)
    console.log('WebSocket连接初始化成功')
  } catch (error) {
    console.error('WebSocket连接初始化失败:', error)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  messageStore.disconnectWebSocket()
})

// 监听当前聊天用户变化，加载聊天历史
watch(
  () => messageStore.currentChatUserId,
  async (newUserId) => {
    if (newUserId) {
      try {
        await messageStore.loadChatHistory(newUserId)
        console.log(`加载用户${newUserId}的聊天历史`)
      } catch (error) {
        console.error('加载聊天历史失败:', error)
      }
    }
  }
)

// 工具函数
const getNameAvatar = (name: string) => {
  // 获取姓名的后两个字作为头像
  return name.length >= 2 ? name.slice(-2) : name
}

// 方法
const selectChat = (chatId: string) => {
  messageStore.setCurrentChatUser(chatId)
}

const showMoreOptions = () => {
  console.log('显示更多选项')
  console.log('WebSocket状态:', wsConnectionStatus.value)
  console.log('未读消息总数:', messageStore.totalUnreadCount)
  // TODO: 实现更多选项功能
}

const handleLogout = () => {
  messageStore.disconnectWebSocket()
  userStore.logout()
  // 触发父组件重新渲染，显示登录页面
  window.location.reload()
}

const insertEmoji = () => {
  console.log('插入表情')
  // TODO: 实现表情选择功能
}

const attachFile = () => {
  console.log('附加文件')
  // TODO: 实现文件上传功能
}

const insertImage = () => {
  console.log('插入图片')
  // TODO: 实现图片上传功能
}

const sendMessage = async (content: string) => {
  if (!content.trim() || !messageStore.currentChatUserId) {
    return
  }

  try {
    const success = await messageStore.sendMessage(messageStore.currentChatUserId, content)
    if (success) {
      console.log('消息发送成功')
    } else {
      console.error('消息发送失败')
    }
  } catch (error) {
    console.error('发送消息时出错:', error)
  }
}
</script>
