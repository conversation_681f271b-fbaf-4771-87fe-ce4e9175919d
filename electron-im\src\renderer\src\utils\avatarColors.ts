// 头像颜色工具函数
// 预定义的头像颜色列表
const AVATAR_COLORS = [
  '#a4d46c', // 浅绿色
  '#7986cb', // 蓝紫色
  '#b0855e', // 棕色
  '#e08f70', // 橙色
  '#f06292', // 粉色
  '#4cb7ad'  // 青色
]

/**
 * 根据用户名生成一致的头像颜色
 * 使用简单的哈希算法确保同一个用户名总是得到相同的颜色
 * @param name 用户名
 * @returns 头像颜色的十六进制值
 */
export function generateAvatarColor(name: string): string {
  if (!name || name.trim() === '') {
    return AVATAR_COLORS[0] // 默认颜色
  }

  // 简单的字符串哈希函数
  let hash = 0
  for (let i = 0; i < name.length; i++) {
    const char = name.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }

  // 确保哈希值为正数
  hash = Math.abs(hash)
  
  // 根据哈希值选择颜色
  const colorIndex = hash % AVATAR_COLORS.length
  return AVATAR_COLORS[colorIndex]
}

/**
 * 获取所有可用的头像颜色
 * @returns 头像颜色数组
 */
export function getAvatarColors(): string[] {
  return [...AVATAR_COLORS]
}

/**
 * 随机获取一个头像颜色
 * @returns 随机的头像颜色
 */
export function getRandomAvatarColor(): string {
  const randomIndex = Math.floor(Math.random() * AVATAR_COLORS.length)
  return AVATAR_COLORS[randomIndex]
}
